import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../core/error/failures.dart';
import '../../entities/test_request/test_request_entity.dart';
import '../../entities/master/customer_entity.dart';
import '../../repositories/test_request_repository.dart';
import '../../repositories/master_repository.dart';

/// Composite use case that performs global search across multiple entities
/// This is useful for implementing a global search feature
class GlobalSearchUseCase {
  final TestRequestRepository testRequestRepository;
  final MasterRepository masterRepository;

  GlobalSearchUseCase({
    required this.testRequestRepository,
    required this.masterRepository,
  });

  Future<Either<Failure, GlobalSearchResult>> call(GlobalSearchParams params) async {
    final List<TestRequestEntity> testRequests = [];
    final List<CustomerEntity> customers = [];
    final List<Failure> failures = [];

    // Search test requests
    final testRequestResult = await testRequestRepository.getTestRequests(
      search: params.query,
      page: 1,
      perPage: params.maxResults,
    );

    testRequestResult.fold(
      (failure) => failures.add(failure),
      (results) => testRequests.addAll(results),
    );

    // Search customers
    final customerResult = await masterRepository.getCustomers(
      search: params.query,
    );

    customerResult.fold(
      (failure) => failures.add(failure),
      (results) => customers.addAll(results.take(params.maxResults)),
    );

    // If all searches failed, return the first failure
    if (failures.length == 2) {
      return Left(failures.first);
    }

    return Right(GlobalSearchResult(
      testRequests: testRequests,
      customers: customers,
      hasErrors: failures.isNotEmpty,
    ));
  }
}

class GlobalSearchParams extends Equatable {
  final String query;
  final int maxResults;

  const GlobalSearchParams({
    required this.query,
    this.maxResults = 10,
  });

  @override
  List<Object> get props => [query, maxResults];
}

class GlobalSearchResult extends Equatable {
  final List<TestRequestEntity> testRequests;
  final List<CustomerEntity> customers;
  final bool hasErrors;

  const GlobalSearchResult({
    required this.testRequests,
    required this.customers,
    required this.hasErrors,
  });

  @override
  List<Object> get props => [testRequests, customers, hasErrors];

  bool get isEmpty => testRequests.isEmpty && customers.isEmpty;
  
  int get totalResults => testRequests.length + customers.length;
}
