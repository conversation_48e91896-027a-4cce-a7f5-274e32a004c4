import 'package:dartz/dartz.dart';
import '../../core/error/exceptions.dart';
import '../../core/error/failures.dart';
import '../../domain/entities/test_request/test_request_entity.dart';
import '../../domain/entities/test_request/sample_entity.dart';
import '../../domain/entities/common/pagination_entity.dart';
import '../../domain/repositories/test_request_repository.dart';
import '../datasources/test_request_remote_data_source.dart';
import '../models/test_request/test_request_model.dart';
import '../models/test_request/sample_model.dart';

class TestRequestRepositoryImpl implements TestRequestRepository {
  final TestRequestRemoteDataSource _remoteDataSource;

  TestRequestRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<Failure, PaginatedResult<TestRequestEntity>>> getTestRequests({
    int page = 1,
    int perPage = 15,
    String? search,
  }) async {
    try {
      final result = await _remoteDataSource.getTestRequests(
        page: page,
        perPage: perPage,
        search: search,
      );

      final entities = result.testRequests.map(_mapTestRequestModelToEntity).toList();
      final paginationEntity = PaginationEntity(
        currentPage: result.pagination.currentPage,
        totalPages: result.pagination.totalPages,
        perPage: result.pagination.perPage,
        total: result.pagination.total,
        hasMore: result.pagination.hasMore,
      );

      final paginatedResult = PaginatedResult<TestRequestEntity>(
        items: entities,
        pagination: paginationEntity,
      );

      return Right(paginatedResult);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestRequestEntity>> getTestRequestById(int id) async {
    try {
      final result = await _remoteDataSource.getTestRequestById(id);
      final entity = _mapTestRequestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestRequestEntity>> createTestRequest(
    TestRequestEntity testRequest,
  ) async {
    try {
      final model = _mapTestRequestEntityToModel(testRequest);
      final result = await _remoteDataSource.createTestRequest(model);
      final entity = _mapTestRequestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TestRequestEntity>> updateTestRequest(
    int id,
    TestRequestEntity testRequest,
  ) async {
    try {
      final model = _mapTestRequestEntityToModel(testRequest);
      final result = await _remoteDataSource.updateTestRequest(id, model);
      final entity = _mapTestRequestModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteTestRequest(int id) async {
    try {
      await _remoteDataSource.deleteTestRequest(id);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<SampleEntity>>> getSamples(int testRequestId) async {
    try {
      final result = await _remoteDataSource.getSamples(testRequestId);
      final entities = result.map(_mapSampleModelToEntity).toList();
      return Right(entities);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, SampleEntity>> addSample(
    int testRequestId,
    SampleEntity sample,
  ) async {
    try {
      final model = _mapSampleEntityToModel(sample);
      final result = await _remoteDataSource.addSample(testRequestId, model);
      final entity = _mapSampleModelToEntity(result);
      return Right(entity);
    } on ServerException catch (e) {
      return Left(ServerFailure(
        message: e.message,
        statusCode: e.statusCode,
      ));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(UnauthorizedFailure(message: e.message));
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'An unexpected error occurred'));
    }
  }

  // Helper methods for mapping between models and entities
  TestRequestEntity _mapTestRequestModelToEntity(TestRequestModel model) {
    return TestRequestEntity(
      id: model.id,
      requestNumber: model.requestNumber,
      requestDate: DateTime.parse(model.requestDate),
      customerName: model.customerName,
      customerAddress: model.customerAddress,
      contactPerson: model.contactPerson,
      mobileNo: model.mobileNo,
      email: model.email,
      specialRequest: model.specialRequest,
      submittedByName: model.submittedByName,
      submittedByDesignation: model.submittedByDesignation,
      submittedByDate: DateTime.parse(model.submittedByDate),
      submittedByIdProof: model.submittedByIdProof,
      receivedByName: model.receivedByName,
      receivedByDesignation: model.receivedByDesignation,
      receivedByDate: DateTime.parse(model.receivedByDate),
      receivedByIdProof: model.receivedByIdProof,
      sampleReceivedTime: DateTime.parse(model.sampleReceivedTime),
      sampleCollectionTime: DateTime.parse(model.sampleCollectionTime),
      quantityOfSample: model.quantityOfSample,
      typeOfSample: model.typeOfSample,
      sampleDetails: model.sampleDetails,
      sampleCode: model.sampleCode,
      samples: model.samples?.map(_mapSampleModelToEntity).toList(),
      createdAt: model.createdAt != null ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  TestRequestModel _mapTestRequestEntityToModel(TestRequestEntity entity) {
    return TestRequestModel(
      id: entity.id,
      requestNumber: entity.requestNumber,
      requestDate: entity.requestDate.toIso8601String().split('T')[0],
      customerName: entity.customerName,
      customerAddress: entity.customerAddress,
      contactPerson: entity.contactPerson,
      mobileNo: entity.mobileNo,
      email: entity.email,
      specialRequest: entity.specialRequest,
      submittedByName: entity.submittedByName,
      submittedByDesignation: entity.submittedByDesignation,
      submittedByDate: entity.submittedByDate.toIso8601String().split('T')[0],
      submittedByIdProof: entity.submittedByIdProof,
      receivedByName: entity.receivedByName,
      receivedByDesignation: entity.receivedByDesignation,
      receivedByDate: entity.receivedByDate.toIso8601String().split('T')[0],
      receivedByIdProof: entity.receivedByIdProof,
      sampleReceivedTime: entity.sampleReceivedTime.toIso8601String(),
      sampleCollectionTime: entity.sampleCollectionTime.toIso8601String(),
      quantityOfSample: entity.quantityOfSample,
      typeOfSample: entity.typeOfSample,
      sampleDetails: entity.sampleDetails,
      sampleCode: entity.sampleCode,
      samples: entity.samples?.map(_mapSampleEntityToModel).toList(),
      createdAt: entity.createdAt?.toIso8601String(),
      updatedAt: entity.updatedAt?.toIso8601String(),
    );
  }

  SampleEntity _mapSampleModelToEntity(SampleModel model) {
    return SampleEntity(
      id: model.id,
      parameterId: model.parameterId,
      particulars: model.particulars,
      type: model.type,
      quantity: model.quantity,
      remarks: model.remarks,
      testRequestId: model.testRequestId,
      createdAt: model.createdAt != null ? DateTime.tryParse(model.createdAt!) : null,
      updatedAt: model.updatedAt != null ? DateTime.tryParse(model.updatedAt!) : null,
    );
  }

  SampleModel _mapSampleEntityToModel(SampleEntity entity) {
    return SampleModel(
      id: entity.id,
      parameterId: entity.parameterId,
      particulars: entity.particulars,
      type: entity.type,
      quantity: entity.quantity,
      remarks: entity.remarks,
      testRequestId: entity.testRequestId,
      createdAt: entity.createdAt?.toIso8601String(),
      updatedAt: entity.updatedAt?.toIso8601String(),
    );
  }
}
