import 'package:flutter/material.dart';

class AnalystJobsScreen extends StatefulWidget {
  const AnalystJobsScreen({super.key});

  @override
  State<AnalystJobsScreen> createState() => _AnalystJobsScreenState();
}

class _AnalystJobsScreenState extends State<AnalystJobsScreen> {
  final List<Map<String, dynamic>> _assignedJobs = [
    {
      'id': 'T001',
      'sampleId': 'S001',
      'customerName': 'ABC Corp',
      'testType': 'Chemical Analysis',
      'priority': 'High',
      'status': 'In Progress',
      'parameters': [
        {'name': 'pH', 'value': '', 'unit': ''},
        {'name': 'Temperature', 'value': '', 'unit': '°C'},
        {'name': 'Concentration', 'value': '', 'unit': 'mg/L'},
      ],
    },
    {
      'id': 'T002',
      'sampleId': 'S002',
      'customerName': 'XYZ Ltd',
      'testType': 'Physical Properties',
      'priority': 'Normal',
      'status': 'Pending',
      'parameters': [
        {'name': 'Density', 'value': '', 'unit': 'g/cm³'},
        {'name': 'Viscosity', 'value': '', 'unit': 'cP'},
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Jobs'),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: _assignedJobs.length,
        itemBuilder: (context, index) {
          final job = _assignedJobs[index];
          return _buildJobCard(context, job);
        },
      ),
    );
  }

  Widget _buildJobCard(BuildContext context, Map<String, dynamic> job) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Test #${job['id']}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildStatusChip(job['status']),
              ],
            ),
            const SizedBox(height: 8),
            Text('Sample ID: ${job['sampleId']}'),
            Text('Customer: ${job['customerName']}'),
            Text('Test Type: ${job['testType']}'),
            const SizedBox(height: 16),
            const Text(
              'Parameters',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...(job['parameters'] as List).map((param) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(param['name']),
                    ),
                    Expanded(
                      flex: 2,
                      child: TextFormField(
                        decoration: InputDecoration(
                          hintText: 'Enter value',
                          suffixText: param['unit'],
                          border: const OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          // TODO: Save parameter value
                        },
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(
                  onPressed: () {
                    // TODO: Implement job rejection
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Job rejected'),
                      ),
                    );
                  },
                  child: const Text('Reject'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement job submission
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Results submitted successfully'),
                      ),
                    );
                  },
                  child: const Text('Submit Results'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    switch (status.toLowerCase()) {
      case 'in progress':
        color = Colors.orange;
        break;
      case 'pending':
        color = Colors.blue;
        break;
      case 'completed':
        color = Colors.green;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        status,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: color,
    );
  }
} 