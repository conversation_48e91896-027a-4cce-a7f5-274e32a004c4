import 'package:flutter/material.dart';

class JobAllocationScreen extends StatefulWidget {
  const JobAllocationScreen({super.key});

  @override
  State<JobAllocationScreen> createState() => _JobAllocationScreenState();
}

class _JobAllocationScreenState extends State<JobAllocationScreen> {
  final List<Map<String, dynamic>> _pendingJobs = [
    {
      'id': 'T001',
      'sampleId': 'S001',
      'customerName': 'ABC Corp',
      'testType': 'Chemical Analysis',
      'priority': 'High',
      'status': 'Pending',
    },
    {
      'id': 'T002',
      'sampleId': 'S002',
      'customerName': 'XYZ Ltd',
      'testType': 'Physical Properties',
      'priority': 'Normal',
      'status': 'Pending',
    },
  ];

  final List<String> _analysts = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Job Allocation'),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: _pendingJobs.length,
        itemBuilder: (context, index) {
          final job = _pendingJobs[index];
          return _buildJobCard(context, job);
        },
      ),
    );
  }

  Widget _buildJobCard(BuildContext context, Map<String, dynamic> job) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Test #${job['id']}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildPriorityChip(job['priority']),
              ],
            ),
            const SizedBox(height: 8),
            Text('Sample ID: ${job['sampleId']}'),
            Text('Customer: ${job['customerName']}'),
            Text('Test Type: ${job['testType']}'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Assign to',
                      border: OutlineInputBorder(),
                    ),
                    items: _analysts.map((String analyst) {
                      return DropdownMenuItem<String>(
                        value: analyst,
                        child: Text(analyst),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      // TODO: Implement analyst assignment
                    },
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement job assignment
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Job assigned successfully'),
                      ),
                    );
                  },
                  child: const Text('Assign'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityChip(String priority) {
    Color color;
    switch (priority.toLowerCase()) {
      case 'high':
        color = Colors.red;
        break;
      case 'normal':
        color = Colors.blue;
        break;
      case 'low':
        color = Colors.green;
        break;
      default:
        color = Colors.grey;
    }

    return Chip(
      label: Text(
        priority,
        style: const TextStyle(color: Colors.white),
      ),
      backgroundColor: color,
    );
  }
} 