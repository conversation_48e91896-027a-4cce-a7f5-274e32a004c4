import 'package:flutter/material.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: 10, // TODO: Replace with actual notifications count
        itemBuilder: (context, index) {
          return _buildNotificationCard(context, index);
        },
      ),
    );
  }

  Widget _buildNotificationCard(BuildContext context, int index) {
    // TODO: Replace with actual notification data
    final isNewJob = index % 2 == 0;
    final title = isNewJob ? 'New Job Assigned' : 'Job Status Updated';
    final subtitle = isNewJob
        ? 'You have been assigned a new test job'
        : 'Test #${index + 1} has been ${index % 3 == 0 ? 'approved' : 'rejected'}';
    final icon = isNewJob ? Icons.work : Icons.update;
    final color = isNewJob ? Colors.blue : Colors.orange;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.2),
          child: Icon(icon, color: color),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        trailing: Text(
          '2h ago', // TODO: Replace with actual timestamp
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        onTap: () {
          // TODO: Navigate to relevant screen based on notification type
        },
      ),
    );
  }
} 