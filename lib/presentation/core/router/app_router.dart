import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lims_app_flutter/presentation/features/auth/screens/forgot_password_screen.dart';
import 'package:lims_app_flutter/presentation/features/auth/screens/login_screen.dart';
import 'package:lims_app_flutter/presentation/features/dashboard/screens/dashboard_screen.dart';
import 'package:lims_app_flutter/presentation/features/jobs/screens/analyst_jobs_screen.dart';
import 'package:lims_app_flutter/presentation/features/jobs/screens/job_allocation_screen.dart';
import 'package:lims_app_flutter/presentation/features/notifications/screens/notifications_screen.dart';
import 'package:lims_app_flutter/presentation/features/tests/screens/create_test_screen.dart';

final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>();

final goRouter = GoRouter(
  navigatorKey: _rootNavigatorKey,
  initialLocation: '/login',
  routes: [
    // Auth routes
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginScreen(),
    ),
    GoRoute(
      path: '/forgot-password',
      builder: (context, state) => const ForgotPasswordScreen(),
    ),

    // Dashboard route
    GoRoute(
      path: '/dashboard',
      builder: (context, state) {
        final role = state.extra as String? ?? 'frontDesk';
        return DashboardScreen(
          userRole: _getUserRole(role),
        );
      },
    ),

    // Front Desk routes
    GoRoute(
      path: '/create-test',
      builder: (context, state) => const CreateTestScreen(),
    ),

    // Chief Chemist routes
    GoRoute(
      path: '/job-allocation',
      builder: (context, state) => const JobAllocationScreen(),
    ),

    // Analyst routes
    GoRoute(
      path: '/analyst-jobs',
      builder: (context, state) => const AnalystJobsScreen(),
    ),

    // Common routes
    GoRoute(
      path: '/notifications',
      builder: (context, state) => const NotificationsScreen(),
    ),
  ],
  errorBuilder: (context, state) => Scaffold(
    body: Center(
      child: Text('Error: ${state.error}'),
    ),
  ),
);

UserRole _getUserRole(String role) {
  switch (role.toLowerCase()) {
    case 'master':
      return UserRole.master;
    case 'frontdesk':
      return UserRole.frontDesk;
    case 'chiefchemist':
      return UserRole.chiefChemist;
    case 'analyst':
      return UserRole.analyst;
    default:
      return UserRole.frontDesk;
  }
} 