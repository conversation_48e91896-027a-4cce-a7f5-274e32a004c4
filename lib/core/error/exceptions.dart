class ServerException implements Exception {
  final String message;
  final int? statusCode;

  const ServerException({
    required this.message,
    this.statusCode,
  });

  @override
  String toString() => 'ServerException: $message (Status: $statusCode)';
}

class NetworkException implements Exception {
  final String message;

  const NetworkException({
    required this.message,
  });

  @override
  String toString() => 'NetworkException: $message';
}

class CacheException implements Exception {
  final String message;

  const CacheException({
    required this.message,
  });

  @override
  String toString() => 'CacheException: $message';
}

class ValidationException implements Exception {
  final String message;
  final Map<String, List<String>>? fieldErrors;

  const ValidationException({
    required this.message,
    this.fieldErrors,
  });

  /// Get errors for a specific field
  List<String> getFieldErrors(String fieldName) {
    return fieldErrors?[fieldName] ?? [];
  }

  /// Get the first error for a specific field
  String? getFirstFieldError(String fieldName) {
    final errors = getFieldErrors(fieldName);
    return errors.isNotEmpty ? errors.first : null;
  }

  /// Check if there are errors for a specific field
  bool hasFieldError(String fieldName) {
    return getFieldErrors(fieldName).isNotEmpty;
  }

  /// Get all field names that have errors
  List<String> get errorFields {
    return fieldErrors?.keys.toList() ?? [];
  }

  /// Get total number of field errors
  int get totalFieldErrors {
    return fieldErrors?.values.fold(0, (sum, errors) => sum + errors.length) ?? 0;
  }

  @override
  String toString() {
    if (fieldErrors != null && fieldErrors!.isNotEmpty) {
      final fieldErrorsStr = fieldErrors!.entries
          .map((entry) => '${entry.key}: ${entry.value.join(', ')}')
          .join('; ');
      return 'ValidationException: $message (Field errors: $fieldErrorsStr)';
    }
    return 'ValidationException: $message';
  }
}

class UnauthorizedException implements Exception {
  final String message;

  const UnauthorizedException({
    required this.message,
  });

  @override
  String toString() => 'UnauthorizedException: $message';
}
