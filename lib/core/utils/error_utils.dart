import '../error/failures.dart';

/// Utility class for handling errors in the UI
class ErrorUtils {
  /// Get a user-friendly error message from a Failure
  static String getErrorMessage(Failure failure) {
    if (failure is ValidationFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return failure.message;
    } else if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is UnauthorizedFailure) {
      return 'Session expired. Please login again.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Get field-specific error message for form validation
  static String? getFieldError(Failure? failure, String fieldName) {
    if (failure is ValidationFailure) {
      return failure.getFirstFieldError(fieldName);
    }
    return null;
  }

  /// Check if a specific field has validation errors
  static bool hasFieldError(Failure? failure, String fieldName) {
    if (failure is ValidationFailure) {
      return failure.hasFieldError(fieldName);
    }
    return false;
  }

  /// Get all field errors as a map for form validation
  static Map<String, String> getFieldErrors(Failure? failure) {
    if (failure is ValidationFailure && failure.fieldErrors != null) {
      final Map<String, String> fieldErrors = {};
      failure.fieldErrors!.forEach((key, value) {
        if (value.isNotEmpty) {
          fieldErrors[key] = value.first;
        }
      });
      return fieldErrors;
    }
    return {};
  }

  /// Get all field errors as a formatted string
  static String getFormattedFieldErrors(Failure? failure) {
    if (failure is ValidationFailure && failure.fieldErrors != null) {
      final List<String> errors = [];
      failure.fieldErrors!.forEach((key, value) {
        if (value.isNotEmpty) {
          final fieldName = _formatFieldName(key);
          errors.add('$fieldName: ${value.first}');
        }
      });
      return errors.join('\n');
    }
    return '';
  }

  /// Convert snake_case field names to human-readable format
  static String _formatFieldName(String fieldName) {
    return fieldName
        .split('_')
        .map((word) => word.isNotEmpty 
            ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
            : word)
        .join(' ');
  }

  /// Show appropriate error message based on failure type
  static String getSnackBarMessage(Failure failure) {
    if (failure is ValidationFailure) {
      if (failure.fieldErrors != null && failure.fieldErrors!.isNotEmpty) {
        return 'Please fix the validation errors and try again.';
      }
      return failure.message;
    } else if (failure is NetworkFailure) {
      return 'Network error. Please check your connection.';
    } else if (failure is UnauthorizedFailure) {
      return 'Session expired. Please login again.';
    } else if (failure is ServerFailure) {
      return 'Server error. Please try again later.';
    } else {
      return 'An unexpected error occurred.';
    }
  }

  /// Get error color based on failure type
  static String getErrorType(Failure failure) {
    if (failure is ValidationFailure) {
      return 'validation';
    } else if (failure is NetworkFailure) {
      return 'network';
    } else if (failure is UnauthorizedFailure) {
      return 'unauthorized';
    } else if (failure is ServerFailure) {
      return 'server';
    } else {
      return 'unknown';
    }
  }

  /// Check if failure requires user to re-authenticate
  static bool requiresReauth(Failure failure) {
    return failure is UnauthorizedFailure;
  }

  /// Check if failure is retryable
  static bool isRetryable(Failure failure) {
    return failure is NetworkFailure || failure is ServerFailure;
  }

  /// Get retry message for retryable failures
  static String getRetryMessage(Failure failure) {
    if (failure is NetworkFailure) {
      return 'Check your connection and try again';
    } else if (failure is ServerFailure) {
      return 'Server temporarily unavailable. Try again';
    }
    return 'Try again';
  }
}

/// Extension methods for easier error handling
extension FailureExtensions on Failure {
  /// Get user-friendly error message
  String get userMessage => ErrorUtils.getErrorMessage(this);
  
  /// Get snackbar message
  String get snackBarMessage => ErrorUtils.getSnackBarMessage(this);
  
  /// Get error type
  String get errorType => ErrorUtils.getErrorType(this);
  
  /// Check if requires re-authentication
  bool get requiresReauth => ErrorUtils.requiresReauth(this);
  
  /// Check if retryable
  bool get isRetryable => ErrorUtils.isRetryable(this);
  
  /// Get retry message
  String get retryMessage => ErrorUtils.getRetryMessage(this);
}

/// Extension methods for ValidationFailure
extension ValidationFailureExtensions on ValidationFailure {
  /// Get field error for a specific field
  String? fieldError(String fieldName) => getFirstFieldError(fieldName);
  
  /// Check if field has error
  bool hasError(String fieldName) => hasFieldError(fieldName);
  
  /// Get all field errors as formatted string
  String get formattedErrors => ErrorUtils.getFormattedFieldErrors(this);
  
  /// Get field errors as map
  Map<String, String> get fieldErrorsMap => ErrorUtils.getFieldErrors(this);
}
