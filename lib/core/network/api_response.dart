import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

// Generic API response for all endpoints
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? errors;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);
}

// Pagination metadata for test-requests API
@JsonSerializable()
class PaginationMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;

  @JsonKey(name: 'total_pages')
  final int totalPages;

  @Json<PERSON>ey(name: 'per_page')
  final int perPage;

  final int total;

  @JsonKey(name: 'has_more')
  final bool hasMore;

  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.perPage,
    required this.total,
    required this.hasMore,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

// Simple response for operations that don't return data
@JsonSerializable()
class SimpleApiResponse {
  final bool success;
  final String? message;

  const SimpleApiResponse({
    required this.success,
    this.message,
  });

  factory SimpleApiResponse.fromJson(Map<String, dynamic> json) =>
      _$SimpleApiResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SimpleApiResponseToJson(this);
}
